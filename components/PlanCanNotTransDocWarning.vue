<template>
    <div
        class="relative p-4 mb-4 text-purple-800 border border-purple-300 rounded-lg bg-purple-50 dark:bg-gray-800 dark:text-purple-300 dark:border-purple-800"
        role="alert"
    >
        <div class="flex items-center">
            <svg
                aria-hidden="true"
                class="w-5 h-5 mr-2"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clip-rule="evenodd"
                ></path>
            </svg>
            <span class="sr-only">Info</span>
            <h3 class="text-lg font-medium">{{ $t("🎉 Special Offer: GPT-4o-mini Unlimited!") }}</h3>
        </div>
        <div class="mt-2 mb-4 text-sm">
            <p class="mb-2">
                {{ $t('PLAN_CAN_NOT_TRAN_DOC') }}
            </p>
            <p class="font-semibold text-purple-700 dark:text-purple-300">
                {{ $t('Get unlimited access to GPT-4o-mini for an entire year! Perfect for users who want consistent, high-quality translations without worrying about token limits.') }}
            </p>
        </div>
        
        <!-- Price Display -->
        <div class="text-center mb-4 p-3 bg-gradient-to-r from-purple-100 to-blue-100 dark:from-purple-900 dark:to-blue-900 rounded-lg">
            <div class="flex justify-center items-baseline">
                <span class="text-3xl font-extrabold text-purple-700 dark:text-purple-300">$10</span>
                <span class="text-gray-600 dark:text-gray-400 ml-1">/{{ $t('year') }}</span>
            </div>
            <p class="text-lg font-semibold text-purple-800 dark:text-purple-200 mt-1">
                {{ $t('GPT-4o-mini Unlimited') }}
            </p>
        </div>

        <!-- Features List -->
        <ul role="list" class="mb-4 space-y-2 text-left text-sm">
            <li class="flex items-center space-x-2">
                <svg class="flex-shrink-0 w-4 h-4 text-purple-500 dark:text-purple-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span>{{ $t("Unlimited GPT-4o-mini usage for 1 year") }}</span>
            </li>
            <li class="flex items-center space-x-2">
                <svg class="flex-shrink-0 w-4 h-4 text-purple-500 dark:text-purple-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span>{{ $t("Unlimited document translation") }}</span>
            </li>
            <li class="flex items-center space-x-2">
                <svg class="flex-shrink-0 w-4 h-4 text-purple-500 dark:text-purple-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span>{{ $t("No Ads") }}</span>
            </li>
        </ul>

        <div class="flex flex-col space-y-2">
            <button
                @click="purchaseUnlimitedPlan"
                type="button"
                class="w-full text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:ring-4 focus:outline-none focus:ring-purple-300 dark:focus:ring-purple-800 shadow-lg font-medium rounded-lg text-sm px-5 py-3 text-center"
            >
                {{ $t('Get GPT-4o-mini Unlimited - $10/year') }}
            </button>
            <button
                @click="router.push({ path: '/pricing-plans' })"
                type="button"
                class="w-full text-purple-700 bg-purple-100 hover:bg-purple-200 focus:ring-4 focus:outline-none focus:ring-purple-300 dark:bg-purple-800 dark:text-purple-200 dark:hover:bg-purple-700 dark:focus:ring-purple-800 font-medium rounded-lg text-sm px-5 py-2 text-center"
            >
                {{ $t('View All Plans') }}
            </button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import PaymentModal from '~/base-components/PaymentModal.vue'
import { useAuthStore } from '~/stores/auth'
import { storeToRefs } from 'pinia'
import { useModal } from 'vue-final-modal'

const router = useRouter()
const emit = defineEmits(['dismiss'])

// Payment modal setup
const { open, close, options } = useModal({
  component: PaymentModal,
  attrs: {
    onClose() {
      close();
    },
    onSubmit() {
      close();
    },
  },
});

// Stores
const authStore = useAuthStore()
const { isLoggedIn } = storeToRefs(authStore)

// Extend feature product (BP0001)
const extendFeatureProduct = ref({
  product_id: 'BP0001',
  price: 10,
  name: 'GPT-4o-mini Unlimited'
})

const purchaseUnlimitedPlan = () => {
  if (!isLoggedIn.value) {
    router.push("/signin");
    return;
  }

  // @ts-ignore
  options.attrs.type = "extend-feature";
  // @ts-ignore
  options.attrs.plan = extendFeatureProduct.value;
  // @ts-ignore
  options.attrs.id = extendFeatureProduct.value.product_id;
  // @ts-ignore
  options.attrs.quantity = 1;
  open();
}
</script>
