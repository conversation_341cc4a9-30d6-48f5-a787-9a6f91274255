<template>
    <div
        class="flex flex-col h-full max-h-full px-4 bg-gray-100 border border-gray-200 rounded-md dark:bg-gray-800 dark:border-gray-600 text-sm text-gray-900 dark:text-gray-100"
    >
        <div v-if="!resultFileLink" class="flex-1 justify-center flex flex-inline items-center">
            <div v-if="isNotEnoughTokens" class="shrink-0">
                <NotEnoughTokensWarning />
            </div>
            <div v-else-if="isHasLimitedPlan" class="shrink-0">
                <PlanFileExceedWarning />
            </div>
            <div v-else-if="isPlanCanNotTransDoc" class="shrink-0 max-w-lg pt-4">
                <PlanCanNotTransDocWarning />
            </div>
            <div
                v-else
                class="flex p-4 mb-4 text-sm text-primary-800 rounded-lg bg-primary-100 dark:bg-gray-800 dark:text-primary-400"
                role="alert"
            >
                <svg
                    aria-hidden="true"
                    class="flex-shrink-0 inline w-5 h-5 mr-3"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        fill-rule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                        clip-rule="evenodd"
                    ></path>
                </svg>
                <span class="sr-only">Info</span>
                <div>
                    <!-- <p v-if="uploadProgress && uploadProgress < 100" class="font-medium animate-pulse">{{ $t('Upload progress') }}: {{uploadProgress}}%</p>
                    <p v-if="uploadProgress && uploadProgress === 100" class="font-medium animate-pulse">{{ $t('File upload completed! Starting to translate...') }}</p>
                    <p v-if="documentResult && documentResult === 'STARTED'" class="font-medium">{{ $t('File translation started!') }}</p> -->
                    <span class="font-medium">{{ $t('After the file translation process is completed') }}:</span>
                    <ul class="mt-1.5 ml-4 list-disc list-inside">
                        <li>{{ $t('You will receive an email with a link to download the file') }}</li>
                        <li>{{ $t('Or you can download the file directly from the History') }}</li>
                    </ul>
                </div>
            </div>
        </div>
        <div v-else class="flex-1">
            <div
                v-if="inputFile && !isHasLimited"
                class="h-full flex flex-col items-center justify-center w-full cursor-pointer space-y-3"
            >
                <a
                    href="#"
                    class="flex flex-inline space-x-3 max-w-lg p-4 bg-primary-100 rounded-lg hover:bg-primary-200 dark:bg-primary-800 dark:border-gray-700 dark:hover:bg-primary-900"
                >
                    <div>
                        <svg
                            class="w-10 h-10 mb-3 text-gray-400"
                            aria-hidden="true"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="1.5"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            ></path>
                        </svg>
                    </div>
                    <div class="truncate">
                        <p class="font-normal text-gray-700 dark:text-gray-400 truncate">
                            {{ $t('Translated') }}_{{ inputFile.name }}
                        </p>
                        <p class="text-sm font-thin text-gray-700 dark:text-gray-400">
                            {{ (inputFile.size / (1000 * 1000)).toFixed(1) }} MB
                        </p>
                    </div>
                </a>
                <a
                    :href="resultFileLink"
                    target="_blank"
                    class="cursor-pointer inline-flex items-center px-3 py-1.5 text-sm font-medium text-primary-900 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:outline-none focus:ring-gray-200 focus:text-primary-700 dark:bg-gray-800 dark:text-primary-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-700"
                >
                    <svg
                        class="w-5 h-5 mr-2 -ml-1"
                        aria-hidden="true"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="1.5"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m.75 12l3 3m0 0l3-3m-3 3v-6m-1.5-9H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                        ></path>
                    </svg>
                    {{ $t('Download') }}
                </a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import LimitedWarning from '~/components/LimitedWarning.vue'
import { storeToRefs } from 'pinia'
import { useTranslateStore } from '~/stores/translate'
import { useAuthStore } from '~/stores/auth'
import NotEnoughTokensWarning from '~/components/NotEnoughTokensWarning.vue'
import PlanFileExceedWarning from '~/components/PlanFileExceedWarning.vue'
import PlanCanNotTransDocWarning from '~/components/PlanCanNotTransDocWarning.vue'
const translateStore = useTranslateStore()
const authStore = useAuthStore()
const { inputFile, resultFileLink, isHasLimited, uploadProgress, documentResult, translateError, isHasLimitedPlan, isPlanCanNotTransDoc } =
    storeToRefs(translateStore)
const { isNotEnoughTokens } = storeToRefs(authStore)
</script>
