{"name": "nuxt-app", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "translate-locales": "./node_modules/.bin/translate-locales"}, "devDependencies": {"@iconify/vue": "4.1.1", "@nuxt/devtools": "latest", "@nuxtjs/pwa": "3.3.5", "@nuxtjs/tailwindcss": "6.7.0", "@types/node": "18", "nuxt": "3.17.5", "tailwind-scrollbar": "3.0.4", "vue-i18n": "9.2.2"}, "dependencies": {"@paypal/paypal-js": "7.0.2", "@pinia/nuxt": "0.4.11", "@sjmc11/tourguidejs": "0.0.10", "@tailwindcss/line-clamp": "0.4.4", "@types/lodash": "4.14.195", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.3", "axios": "1.12.0", "click-outside-vue3": "4.0.1", "dayjs": "1.11.8", "defu": "^6.1.4", "firebase": "9.23.0", "flowbite": "1.8.1", "languagedetect": "2.0.0", "lodash": "4.17.21", "pinia": "2.1.3", "sse.js": "0.6.1", "translate-locales": "1.1.2", "vue-final-modal": "4.4.2", "vue-loading-overlay": "6.0.3", "vue-number-animation": "1.1.2", "vue-writer": "1.2.0", "vue3-slider": "1.9.0"}, "packageManager": "pnpm@8.9.2+sha1.5f2fa48d614263457cf5d7fb7be8b878da318d87"}