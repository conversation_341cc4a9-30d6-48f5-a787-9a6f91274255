<script setup>
import { reactive } from 'vue'
import Loading from 'vue-loading-overlay'
import TheLogoTranslate from '~/components/TheLogoTranslate.vue'
import { useAuthStore } from '~/stores/auth'
import { storeToRefs } from 'pinia'
import { useVuelidate } from '@vuelidate/core'
import * as validators from '@vuelidate/validators'
import { useI18n } from 'vue-i18n'
const config = useRuntimeConfig()
const i18n = useI18n()
const { createI18nMessage } = validators
const withI18nMessage = createI18nMessage({ t: i18n.t.bind(i18n) })
const required = withI18nMessage(validators.required)
const email = withI18nMessage(validators.email)
const minLength = (v) => withI18nMessage(validators.minLength(v))

const authStore = useAuthStore()
const { isLoggingIn, isError, signInError } = storeToRefs(authStore)

const formLoginState = reactive({
    username: '',
    password: '',
    remember_me: false,
})
const formLoginRules = computed(() => ({
    username: { required, email },
    password: { required, minLength: minLength(6) },
}))

const v$ = useVuelidate(formLoginRules, formLoginState)
const onLogin = async () => {
    const result = await v$.value.$validate()
    if (!result) {
        return
    }

    const success = await authStore.login({
        username: formLoginState.username,
        password: formLoginState.password,
        remember_me: formLoginState.remember_me,
    })
    if (success) {
        // hard reload to home page
        window.location.href =  "/"
    }
}
definePageMeta({
    layout: false,
})

const router = useRouter()

const canLoginBySocial = !config.public.NUXT_DISABLE_LOGIN_BY_SOCIAL
</script>

<template>
    <NuxtLayout name="animation">
        <div class="flex flex-col items-center px-6 py-8 mx-auto md:min-h-screen lg:py-14">
            <div class="flex items-center text-white mb-8 w-full sm:max-w-lg">
                <TheLogoTranslate />
            </div>
            <div
                class="relative w-full bg-white rounded-lg shadow dark:border md:mt-0 sm:max-w-lg xl:p-0 dark:bg-gray-800 dark:border-gray-700 z-20"
            >
                <loading height="35" v-model:active="isLoggingIn" :is-full-page="false" />
                <div class="p-6 space-y-4 md:space-y-4 sm:p-8">
                    <h1
                        class="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white"
                    >
                        {{ $t('Sign in to your account') }}
                    </h1>
                    <div v-if="canLoginBySocial">
                        <div class="grid grid-cols-1 md:grid-cols-1 gap-3">
                            <LoginByGoogle />
                            <!-- <LoginByApple /> -->
                        </div>
                        <div class="inline-flex items-center justify-center w-full">
                            <hr class="w-full h-px my-0 bg-gray-200 border-0 dark:bg-gray-700">
                            <span class="absolute px-3 text-sm font-light text-gray-900 -translate-x-1/2 bg-white left-1/2 dark:text-white dark:bg-gray-800">
                                {{ $t('OR') }}
                            </span>
                        </div>
                    </div>
                    <form @submit.prevent="onLogin" class="space-y-4 md:space-y-6">
                        <div>
                            <label
                                for="email"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                                :class="{ 'text-red-700 dark:text-red-500': v$.username.$error }"
                            >
                                {{ $t('Your email') }}
                            </label>
                            <input
                                type="email"
                                name="Email Address"
                                id="email"
                                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 placeholder-gray-300 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                placeholder="<EMAIL>"
                                v-model="formLoginState.username"
                                :class="{
                                    'bg-red-50 border border-red-500 text-red-900 placeholder-red-700 focus:ring-red-500 dark:bg-gray-700 focus:border-red-500 dark:text-red-500 dark:placeholder-red-500 dark:border-red-500':
                                        v$.username.$error,
                                }"
                            />
                            <p v-if="v$.username.$errors" class="mt-2 text-sm text-red-600 dark:text-red-500">
                                {{ v$.username.$errors[0]?.$message }}
                            </p>
                        </div>
                        <div>
                            <label
                                for="password"
                                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                                :class="{
                                    'text-red-700 dark:text-red-500': v$.password.$error,
                                }"
                            >
                                {{ $t('Password') }}
                            </label>
                            <input
                                type="password"
                                name="password"
                                id="password"
                                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 placeholder-gray-300 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                placeholder="••••••••"
                                v-model="formLoginState.password"
                                :class="{
                                    'bg-red-50 border border-red-500 text-red-900 placeholder-red-700 focus:ring-red-500 dark:bg-gray-700 focus:border-red-500 dark:text-red-500 dark:placeholder-red-500 dark:border-red-500':
                                        v$.password.$error,
                                }"
                            />
                            <p v-if="v$.password.$errors[0]" class="mt-2 text-sm text-red-600 dark:text-red-500">
                                {{ v$.password.$errors[0]?.$message }}
                            </p>
                        </div>
                        <div
                            v-if="signInError && signInError.detail.error_code === 'ACCOUNT_NOT_VERIFIED'"
                            class="flex p-4 mb-4 text-sm text-yellow-800 border border-yellow-300 rounded-lg bg-yellow-50 dark:bg-gray-800 dark:text-yellow-300 dark:border-yellow-800"
                            role="alert"
                        >
                            <svg
                                aria-hidden="true"
                                class="flex-shrink-0 inline w-5 h-5 mr-3"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                            <span class="sr-only">Info</span>
                            <div>
                                <span class="font-medium">
                                    {{ $t('Account not verified!') }}
                                </span>
                                {{ $t('Please check your mailbox to active your account or') }}
                                <a
                                    class="underline text-sm font-medium text-primary-600 hover:underline dark:text-primary-500 cursor-pointer"
                                    @click="resendActivation"
                                    >Resend activation</a
                                >
                            </div>
                        </div>
                        <div
                            v-if="signInError"
                            class="flex p-4 mb-4 text-sm text-yellow-800 border border-yellow-300 rounded-lg bg-yellow-50 dark:bg-gray-800 dark:text-yellow-300 dark:border-yellow-800"
                            role="alert"
                        >
                            <svg
                                aria-hidden="true"
                                class="flex-shrink-0 inline w-5 h-5 mr-3"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                            <span class="sr-only">Info</span>
                            <div>
                                <span class="font-medium">
                                    {{ $t('Login failed') }}
                                </span>
                                {{ $t(signInError.detail.error_code) }}
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-start">
                                <div class="flex items-center h-5">
                                    <input
                                        id="remember"
                                        aria-describedby="remember"
                                        type="checkbox"
                                        v-model="formLoginState.remember_me"
                                        class="w-4 h-4 text-primary-600 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-primary-600 dark:ring-offset-gray-800"
                                    />
                                </div>
                                <div class="ml-3 text-sm">
                                    <label for="remember" class="text-gray-500 dark:text-gray-300">
                                        {{ $t('Remember me') }}
                                    </label>
                                </div>
                            </div>
                            <a
                                @click="router.push({ path: '/account-recovery' })"
                                class="text-sm font-medium text-primary-600 hover:underline dark:text-primary-500 cursor-pointer"
                            >
                                {{ $t('Forgot password?') }}
                            </a>
                        </div>
                        <button
                            :disabled="isLoggingIn"
                            type="submit"
                            class="w-full text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                        >
                            {{ $t('Sign In') }}
                        </button>
                        <p class="text-sm font-light text-gray-500 dark:text-gray-400">
                            {{ $t("Don't have an account yet?") }}
                            <a
                                @click="router.push({ path: '/signup' })"
                                class="font-medium text-primary-600 hover:underline dark:text-primary-500 cursor-pointer"
                            >
                                {{ $t('Sign Up') }}
                            </a>
                        </p>
                        <p class="text-sm font-light text-gray-500 dark:text-gray-400">
                            <a
                                @click="router.push({ path: '/' })"
                                class="font-medium text-primary-600 hover:underline dark:text-primary-500 cursor-pointer"
                            >
                                {{ $t('Back to home') }}
                            </a>
                        </p>
                    </form>
                </div>
            </div>
        </div>
    </NuxtLayout>
</template>
