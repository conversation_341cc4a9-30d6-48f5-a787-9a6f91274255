import { defineStore } from 'pinia'
import _ from 'lodash'
import LanguageDetect from 'languagedetect'

import { useAPI, useAxios, useStreamAPI } from '~/composables/useAPI'
import { useAuthStore } from './auth'
import { useAppStore } from './app'
import type { TranslateRequest } from '~/interface/common-interface'
import axios from 'axios'
interface TranslateResultResponse {
    success: string
    result: string
    lang: string
    translation_uuid?: string
    warning?: string[]
}

interface TranslateOption {
    translateWritingStyle?: string
    translateDomain?: string
    translateTone?: string
}

export const useTranslateStore = defineStore('translate', {
    state: () => ({
        transFromLanguagesList: [] as string[],
        transToLanguagesList: [] as string[],
        selectedTransFromLang: 'Detect language',
        selectedTransToLang: 'English',
        mode: 'text',
        result: '',
        documentResult: '',
        resultUuid: '',
        inputText: '',
        inputFile: null as File | null,
        resultFileLink: '',
        isTranslating: false,
        isUploading: false,
        openSelectLanguage: false,
        selectLanguageFor: 'from',
        detectedLanguage: '',
        translateOptions: {
            translateDomain: '',
            translateTone: '',
            translateWritingStyle: '',
        } as TranslateOption,
        translateError: '',
        uploadProgress: 0,
        showLimitedWarning: true,
        showAccountVerifyWarning: false,
        customPrompt: '',
        showCustomPrompt: false,
        liked: false,
        disLiked: false,
        invalidFileExtension: ['doc', 'ppt', 'xls'],
        isInvalidFile: false,
        filePassword: '',
        isFileHasPassword: false,
        translateStream: null,
        translatingSesionId: 0,
        translateTextResult: {} as { [key: number]: string },
    }),

    getters: {
        setShowAccountVerifyWarning: (state) => (value: boolean) => {
            state.showAccountVerifyWarning = value
        },
        isHasLimited: (state) => {
            const authStore = useAuthStore()
            const sizeFile = (state.inputFile as File)?.size / (1000 * 1000)
            return !authStore.isLoggedIn && (state.inputText?.length > 500 || sizeFile >= 0)
        },
        isHasLimitedPlan: (state) => {
            return ['PLAN_MAX_FILE_SIZE_EXCEED', 'PLAN_TOTAL_FILE_EXCEED'].includes(state.translateError)
        },
        isPlanCanNotTransDoc: (state) => {
            return state.translateError === 'PLAN_CAN_NOT_TRAN_DOC'
        },
        isDetectLanguage: (state) => state.selectedTransFromLang === 'Detect language',
        showWarningDocument: (state) => {
            const authStore = useAuthStore()
            return state.inputFile && !authStore.isLoggedIn
        },
        isDisableTranslateButton: (state) => (routeName: string) => {
            return (
                (state.isTranslating && routeName == 'documents') ||
                (state.isInvalidFile && routeName == 'documents') ||
                (state.inputText?.trim()?.length === 0 && routeName == 'index') ||
                (state.inputFile === null && routeName == 'documents')
            )
        },
        isFileProtected: (state) => {
            return ['FILE_IS_PASSWORD_PROTECTED', 'FILE_PASSWORD_NOT_CORRECT'].includes(state.translateError)
        },
        isFilePasswordIncorrect: (state) => {
            return state.translateError === 'FILE_PASSWORD_NOT_CORRECT'
        },
    },

    actions: {
        setIsTranslating(value: boolean) {
            this.isTranslating = value
        },
        setMode(mode: string) {
            this.mode = mode
        },
        setInputFile(file: File) {
            //check file type is docx, pptx, xlsx or pdf
            const fileExtension = file.name.split('.').pop()
            if (!['docx', 'pptx', 'xlsx', 'pdf', ...this.invalidFileExtension].includes(fileExtension as string)) {
                return
            }

            this.isInvalidFile = this.invalidFileExtension.includes(fileExtension as string)
            this.inputFile = file
        },
        clearInputFile() {
            this.inputFile = null
            this.resultFileLink = ''
            this.documentResult = ''
            this.translateError = ''
            this.filePassword = ''
            this.isFileHasPassword = false
            this.uploadProgress = 0
        },
        clearTranslateText() {
            this.inputText = ''
            this.result = ''
            this.translateTextResult = {}
            this.translateOptions = {
                translateDomain: '',
                translateTone: '',
                translateWritingStyle: '',
            }
            this.translateError = ''
            this.detectedLanguage = ''
            this.customPrompt = ''
        },
        setSelectLanguageFor(mode: string) {
            this.selectLanguageFor = mode
            this.openSelectLanguage = true
        },
        setOpenSelectLanguage(value: boolean) {
            this.openSelectLanguage = value
        },
        initSelectedLanguages() {
            const defaultFromLanguages = localStorage.getItem('transFromLanguagesList')
                ? JSON.parse(localStorage.getItem('transFromLanguagesList') as string)
                : ['english', 'japanese', 'vietnamese']
            const defaultToLanguages = localStorage.getItem('transToLanguagesList')
                ? JSON.parse(localStorage.getItem('transToLanguagesList') as string)
                : ['english', 'japanese', 'vietnamese']
            const localStorageTransFromLang = localStorage.getItem('transFromLang')
            const localStorageTransToLang = localStorage.getItem('transToLang')

            if (localStorageTransFromLang && localStorageTransFromLang !== 'Detect language') {
                defaultFromLanguages.unshift(localStorageTransFromLang)
                //remove duplicate
            }

            this.transFromLanguagesList = _.take(_.uniq(defaultFromLanguages), 3)

            if (localStorageTransToLang && localStorageTransToLang !== 'Detect language') {
                defaultToLanguages.unshift(localStorageTransToLang)
            }

            this.transToLanguagesList = _.take(_.uniq(defaultToLanguages), 3)

            if (localStorageTransFromLang === 'Detect language') {
                this.selectedTransFromLang = 'Detect language'
            }

            if (!localStorageTransFromLang || localStorageTransFromLang === 'Detect language') {
                this.selectedTransFromLang = 'Detect language'
            } else {
                this.selectedTransFromLang = this.transFromLanguagesList[0]
            }
            if (localStorageTransToLang === 'Detect language') {
                this.selectedTransToLang = 'Detect language'
            } else {
                this.selectedTransToLang = this.transToLanguagesList[0]
            }

            if (this.selectedTransFromLang === this.selectedTransToLang) {
                this.selectedTransToLang = this.transToLanguagesList[1]
            }
        },
        addTransLang(lang: string) {
            this.openSelectLanguage = false
            if (this.selectLanguageFor === 'from') {
                this.addTransFromLang(lang)
                return
            } else {
                this.addTransToLang(lang)
                return
            }
        },
        addTransFromLang(lang: string) {
            this.selectedTransFromLang = lang

            //case 1: lang is already in trans from list
            if (this.transFromLanguagesList.includes(lang)) {
                if (this.selectedTransToLang === lang) {
                    const nextTransToLangIndex = this.transToLanguagesList.findIndex((item) => item !== lang)
                    this.selectedTransToLang = this.transToLanguagesList[nextTransToLangIndex]
                }
                return
            }

            //case 2: lang is already in trans to list
            if (this.selectedTransToLang === lang) {
                const nextTransToLangIndex = this.transToLanguagesList.findIndex((item) => item !== lang)
                this.selectedTransToLang = this.transToLanguagesList[nextTransToLangIndex]
            }
            this.transFromLanguagesList.unshift(lang)
            this.transFromLanguagesList = _.take(_.cloneDeep(this.transFromLanguagesList), 3)
        },

        addTransToLang(lang: string) {
            this.selectedTransToLang = lang

            //case 1: lang is already in trans from list
            if (this.transToLanguagesList.includes(lang)) {
                if (this.selectedTransFromLang === lang) {
                    const nextTransFromLangIndex = this.transFromLanguagesList.findIndex((item) => item !== lang)
                    this.selectedTransFromLang = this.transFromLanguagesList[nextTransFromLangIndex]
                }
                return
            }

            //case 2: lang is already in trans to list
            if (this.selectedTransFromLang === lang) {
                const nextTransFromLangIndex = this.transFromLanguagesList.findIndex((item) => item !== lang)
                this.selectedTransFromLang = this.transFromLanguagesList[nextTransFromLangIndex]
            }
            this.transToLanguagesList.unshift(lang)
            this.transToLanguagesList = _.take(_.cloneDeep(this.transToLanguagesList), 3)
        },

        selectLanguage(mode: string, lang: string) {
            const selectedTransLang = {
                from: this.selectedTransFromLang,
                to: this.selectedTransToLang,
            }

            const transLanguagesList = {
                from: this.transFromLanguagesList,
                to: this.transToLanguagesList,
            }
            type Key = keyof typeof selectedTransLang
            const dataSource = {
                target: selectedTransLang[mode as Key],
                opposite: mode === 'from' ? this.selectedTransToLang : this.selectedTransFromLang,
            }

            if (dataSource.target === lang) {
                return
            }

            if (dataSource.opposite === lang) {
                this.swapLanguages()
                return
            }

            if (mode === 'from') {
                this.setSelectedTransFromLang(lang)
            } else {
                this.setSelectedTransToLang(lang)
            }
        },

        setSelectedTransFromLang(lang: string) {
            this.selectedTransFromLang = lang
        },

        setSelectedTransToLang(lang: string) {
            this.selectedTransToLang = lang
        },
        capitalizeFirstLetter(inputString: string) {
            return inputString.charAt(0).toUpperCase() + inputString.slice(1)
        },
        remove_origin_language_marks(text: string) {
            const match = text.match(/\{.*?\}/)
            let textWithoutSubstring = text
            if (match) {
                const substringToRemove = match[0]
                textWithoutSubstring = text.replace(substringToRemove, '')
            }

            return textWithoutSubstring
        },
        async translate(mode: string = 'index') {
            this.translateError = ''
            this.documentResult = ''
            const authStore = useAuthStore()
            const appStore = useAppStore()
            const custom_prompt = this.showCustomPrompt ? this.customPrompt : ''
            if (authStore.isNotEnoughTokens || this.isInvalidFile) {
                return
            }
            this.isTranslating = true

            this.detectedLanguage = ''
            const config = useRuntimeConfig()
            let publicPrefix = ''
            let payload: TranslateRequest = {
                target_lang: this.selectedTransToLang,
                domain: this.translateOptions.translateDomain,
                tone: this.translateOptions.translateTone,
                writing_style: this.translateOptions.translateWritingStyle,
                trans_input: this.inputText,
                custom_prompt,
            }
            if (!this.isDetectLanguage) {
                payload.origin_lang = this.selectedTransFromLang
            }

            payload.gpt_version = appStore.chatGPTVersion

            if (mode === 'index') {
                this.translatingSesionId += 1
                if (this.translateStream && this.isTranslating) {
                    this.translateStream.close()
                    this.isTranslating = false
                    this.translateStream = null
                    return
                }

                if (this.translateStream) {
                    this.translateStream.close()
                }

                this.result = ''
                this.translateTextResult = {}
                if (!authStore.isLoggedIn) {
                    // User is not logging in then use public API (limited translate)
                    publicPrefix = 'public/'
                }

                const thisStore = this
                const session_id = thisStore.translatingSesionId
                try {
                    const source = useStreamAPI(publicPrefix + 'translate-text-stream', payload)

                    source.addEventListener('message', async function (e: any) {
                        if (e.data != '[DONE]') {
                            try {
                                const payload = JSON.parse(e.data)
                                console.log(payload)
                                if (payload && payload.success && payload.lang) {
                                    thisStore.detectedLanguage = payload.lang
                                } else if (payload && payload.success && payload.result) {
                                    thisStore.result += payload.result
                                    console.log('🚀 ~ file: translate.ts:352 ~ session_id:', session_id)

                                    thisStore.translateTextResult[session_id] =
                                        (thisStore.translateTextResult[session_id] || '') + payload.result
                                } else if (payload.translation_uuid) {
                                    thisStore.resultUuid = payload.translation_uuid
                                }
                                thisStore.showAccountVerifyWarning =
                                    payload?.warning?.includes('ACCOUNT_NOT_VERIFY') || false
                            } catch (ex) {
                                console.log(e.data)
                                thisStore.isTranslating = false
                                thisStore.translateStream = null
                            }
                        } else {
                            source.close()
                            thisStore.isTranslating = false
                            thisStore.translateStream = null
                        }
                    })

                    source.stream()
                    this.translateStream = source
                } catch (error) {
                    thisStore.isTranslating = false
                }
                // }
            } else if (mode === 'documents') {
                if (this.showWarningDocument || authStore.isUnverified) {
                    this.isTranslating = false

                    return
                } else {
                    this.isTranslating = true
                    this.isUploading = true
                    const authStore = useAuthStore()

                    let uploadFileLink: any

                    try {
                        uploadFileLink = await this.getUploadFileLink(
                            this.inputFile?.name || '',
                            this.inputFile?.size || 0
                        )
                    } catch (error: any) {
                        this.isTranslating = false
                        this.translateError = error.detail?.error_code || 'SYSTEM_ERROR'
                        this.documentResult = 'ERROR'
                        return false
                    }

                    const { http_method, url, s3_file_path, file_name_origin } = uploadFileLink
                    if (!uploadFileLink) {
                        this.isTranslating = false
                        this.translateError = 'SYSTEM_ERROR'
                        this.documentResult = 'ERROR'
                        return
                    } else {
                        try {
                            const response = await axios.put(url, this.inputFile, {
                                headers: {
                                    'Content-Type': this.inputFile?.type || '',
                                },
                                onUploadProgress: (progressEvent: any) => {
                                    this.uploadProgress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
                                    console.log(this.uploadProgress)
                                },
                            })
                        } catch (error: any) {
                            console.log(error)
                            this.isTranslating = false
                            this.translateError = error.response.data?.detail?.error_code || 'SYSTEM_ERROR'
                            this.documentResult = 'ERROR'
                            return false
                        }
                    }
                    const formData = new FormData()
                    formData.append('s3_file_path', s3_file_path || '')
                    formData.append('file_name_origin', file_name_origin || '')
                    formData.append('origin_lang', this.selectedTransFromLang)
                    formData.append('target_lang', this.selectedTransToLang)
                    formData.append('domain', this.translateOptions.translateDomain || '')
                    formData.append('tone', this.translateOptions.translateTone || '')
                    formData.append('writing_style', this.translateOptions.translateWritingStyle || '')
                    formData.append('custom_prompt', custom_prompt)
                    formData.append('file_password', this.filePassword)
                    formData.append('gpt_version', appStore.chatGPTVersion)
                    // formdata to json
                    const object = {} as any
                    formData.forEach((value, key) => {
                        object[key] = value
                    })
                    try {
                        const response = await useAxios({
                            url: 'translate-document-s3',
                            method: 'POST',
                            params: {
                                s3_file_path,
                                file_name_origin,
                            },
                            data: formData,
                            // onUploadProgress: (progressEvent: any) => {
                            //     this.uploadProgress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
                            //     console.log(this.uploadProgress)
                            // },
                        })

                        if (response.data.success) {
                            console.log(response.data)
                            this.isTranslating = false
                            this.uploadProgress = 0
                            this.documentResult = 'STARTED'
                        }
                    } catch (error: any) {
                        console.log(error)
                        this.isTranslating = false
                        this.translateError = error.response.data?.detail?.error_code || 'SYSTEM_ERROR'
                        this.documentResult = 'ERROR'
                        return false
                    } finally {
                        this.isUploading = false
                    }
                }
            }

            // Update user tokens after translate
            authStore.syncUserTokenInfo()
        },
        swapLanguages() {
            if (this.selectedTransFromLang === 'Detect language') {
                this.selectedTransFromLang = this.selectedTransToLang
                const currentSelectedToLangIndex = this.transToLanguagesList.indexOf(this.selectedTransToLang)
                this.selectedTransToLang =
                    currentSelectedToLangIndex == 2
                        ? this.transToLanguagesList[0]
                        : this.transToLanguagesList[currentSelectedToLangIndex + 1]
            } else {
                this.selectedTransFromLang = [
                    this.selectedTransToLang,
                    (this.selectedTransToLang = this.selectedTransFromLang),
                ][0]
                if (!this.transFromLanguagesList.includes(this.selectedTransFromLang)) {
                    this.transFromLanguagesList.unshift(this.selectedTransFromLang)
                    this.transFromLanguagesList = _.take(_.cloneDeep(this.transFromLanguagesList), 3)
                }
                if (!this.transToLanguagesList.includes(this.selectedTransToLang)) {
                    this.transToLanguagesList.unshift(this.selectedTransToLang)
                    this.transToLanguagesList = _.take(_.cloneDeep(this.transToLanguagesList), 3)
                }
            }
        },
        setInputText(text: string) {
            this.inputText = text
        },
        clearInputAndResultText() {
            this.result = ''
            this.translateTextResult = {}
            this.setInputText('')
            this.showLimitedWarning = true
            this.detectedLanguage = ''
            this.selectedTransFromLang = 'Detect language'
        },
        clearResultText() {
            this.result = ''
            this.translateTextResult = {}
        },
        detectLanguage() {
            if (this.selectedTransFromLang === 'Detect language') {
                if (this.inputText.length > 0) {
                    const lngDetector = new LanguageDetect()
                    const detectedLang = lngDetector.detect(this.inputText, 1)
                    if (detectedLang.length > 0) {
                        this.detectedLanguage = detectedLang[0][0]
                    }
                } else {
                    this.detectedLanguage = ''
                }
            }
        },
        setShowLimitedWarning(value: boolean) {
            this.showLimitedWarning = value
        },
        setRating(rating: string) {
            if (!rating) {
                this.liked = false
                this.disLiked = false
                return
            }
            this.liked = rating === 'thumbs_up'
            this.disLiked = rating === 'thumbs_down'
        },

        async getUploadFileLink(file_name: string, file_size: number) {
            try {
                const { data, error } = await useAPI('get-upload-file-url', {
                    method: 'GET',
                    // lazy: true,
                    server: false,
                    params: {
                        file_name,
                        file_size,
                    },
                })

                if (data.value) {
                    return data.value
                }
                if (error.value?.data) {
                    throw error.value?.data
                }

                return false
            } catch (error) {
                console.log(error)
                throw error
            }
        },
    },
})
